@extends('layouts.app')

@section('title', $course->title . ' - Escape Matrix Academy')

@section('content')
<!-- Course Hero Section -->
<section class="py-20 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Course Info -->
            <div>
                <div class="mb-6">
                    <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">{{ $course->category ? $course->category->name : $course->category }}</span>
                    <span class="ml-2 bg-gray-700 text-gray-300 px-3 py-1 rounded-full text-sm">{{ ucfirst($course->level) }}</span>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">{{ $course->title }}</h1>

                @if($course->subtitle)
                    <h2 class="text-xl text-gray-300 mb-6 font-medium">{{ $course->subtitle }}</h2>
                @endif

                <p class="text-xl text-gray-400 mb-8 leading-relaxed">{{ Str::limit($course->description, 200) }}</p>
                
                <div class="flex items-center space-x-6 mb-8 text-gray-400">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>{{ $course->duration }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span>{{ $course->instructor->name }}</span>
                    </div>
                </div>

                <!-- Enrollment/Payment Section -->
                @auth
                    @if($isEnrolled)
                        <div class="bg-green-900 border border-green-700 rounded-lg p-6 mb-6">
                            <div class="flex items-center space-x-3 mb-4">
                                <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h3 class="text-green-400 font-semibold">You're enrolled!</h3>
                                    <p class="text-green-300 text-sm">You have full access to this course content and materials.</p>
                                </div>
                            </div>
                            <div class="flex space-x-3">
                                <a href="{{ route('my-courses.view', $course) }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md transition-colors">
                                    Continue Learning
                                </a>
                                <a href="{{ route('courses.materials', $course) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md transition-colors">
                                    View Materials
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <h3 class="text-2xl font-bold text-white">${{ number_format($course->price, 2) }}</h3>
                                    <p class="text-gray-400 text-sm">One-time payment</p>
                                </div>
                                @if($course->featured)
                                    <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs font-medium">FEATURED</span>
                                @endif
                            </div>
                            
                            @if($isEnrolled)
                                <a href="{{ route('my-courses.view', $course) }}" class="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253z"></path>
                                    </svg>
                                    <span>Access Course</span>
                                </a>
                            @else
                                <form action="{{ route('paypal.create', $course) }}" method="POST" class="space-y-4">
                                    @csrf
                                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-md font-semibold transition-colors flex items-center justify-center space-x-2">
                                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.028-.026.056-.052.08-.65 3.85-3.197 5.341-6.957 5.341h-2.504c-.524 0-.968.382-1.05.9L8.937 19.9c-.013.06-.004.119.021.176.067.153.211.261.379.261h2.94c.458 0 .848-.334.922-.788l.04-.207.738-4.68.047-.257c.075-.453.465-.788.922-.788h.58c3.57 0 6.36-1.45 7.17-5.64.34-1.75.17-3.21-.72-4.25-.27-.31-.61-.56-1.01-.72z"/>
                                        </svg>
                                        <span>Pay with PayPal</span>
                                    </button>
                                </form>
                            @endif
                            
                            <div class="mt-4 text-center">
                                <p class="text-gray-400 text-sm">Secure payment powered by PayPal</p>
                                <p class="text-gray-500 text-xs mt-1">30-day money-back guarantee</p>
                            </div>
                        </div>
                    @endif
                @else
                    <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-white mb-4">Ready to start learning?</h3>
                        <p class="text-gray-400 mb-6">Sign in or create an account to enroll in this course.</p>
                        <div class="space-y-3">
                            <a href="{{ route('login') }}" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-6 rounded-md font-semibold transition-colors text-center block">
                                Sign In to Enroll
                            </a>
                            <a href="{{ route('register') }}" class="w-full border border-gray-600 text-gray-300 hover:bg-gray-700 py-3 px-6 rounded-md font-semibold transition-colors text-center block">
                                Create Account
                            </a>
                        </div>
                        <div class="mt-4 text-center">
                            <p class="text-2xl font-bold text-white">${{ number_format($course->price, 2) }}</p>
                            <p class="text-gray-400 text-sm">One-time payment</p>
                        </div>
                    </div>
                @endauth
            </div>

            <!-- Course Image/Preview -->
            <div class="relative">
                @if($course->preview_type === 'video' && $course->preview_content)
                    <video controls class="w-full rounded-lg shadow-2xl">
                        <source src="{{ $course->preview_content }}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                @elseif($course->preview_type === 'youtube' && $course->preview_content)
                    <div class="w-full rounded-lg overflow-hidden shadow-2xl">
                        <iframe 
                            src="{{ $course->preview_content }}" 
                            class="w-full aspect-video" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                @elseif($course->preview_type === 'image' && $course->preview_content)
                    <img src="{{ Storage::url($course->preview_content) }}" alt="{{ $course->title }}" class="w-full rounded-lg shadow-2xl">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-lg"></div>
                @else
                    @if($course->image_url)
                         <img src="{{ $course->image_url }}"
                              alt="{{ $course->title }}"
                              class="w-full rounded-lg shadow-2xl">
                     @else
                         <img src="{{ asset('images/course-template.svg') }}"
                              alt="{{ $course->title }}"
                              class="w-full rounded-lg shadow-2xl">
                     @endif
                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-lg"></div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Course Details -->
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- What You'll Learn -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-8">
                    <h2 class="text-2xl font-bold text-white mb-6">About This Course</h2>
                    <div class="prose prose-invert max-w-none">
                        @if($course->what_youll_learn_text)
                            <div class="text-gray-300 leading-relaxed">
                                {!! \App\Helpers\MarkdownHelper::toHtmlSimple($course->what_youll_learn_text) !!}
                            </div>
                        @elseif($course->what_you_will_learn && is_array($course->what_you_will_learn))
                            <ul class="space-y-2">
                                @foreach($course->what_you_will_learn as $outcome)
                                    <li class="flex items-start space-x-3">
                                        <svg class="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-gray-300">{{ $outcome }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <div class="text-gray-300 leading-relaxed">
                                {!! \App\Helpers\MarkdownHelper::toHtmlSimple($course->description) !!}
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Course Description -->
                <!-- @if($course->description)
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-8">
                    <h2 class="text-2xl font-bold text-white mb-6">About This Course</h2>
                    <div class="prose prose-invert max-w-none">
                        <div class="text-gray-300 leading-relaxed">
                            {!! \App\Helpers\MarkdownHelper::toHtmlSimple($course->description) !!}
                        </div>
                    </div>
                </div>
                @endif -->

                <!-- Course Curriculum -->
                @if($course->chapters->count() > 0)
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-white">Course Curriculum</h2>
                        <div class="text-sm text-gray-400">
                            {{ $course->chapters->count() }} chapters •
                            {{ $totalLectures }} lectures •
                            {{ floor($totalDuration / 60) }}h {{ $totalDuration % 60 }}m
                        </div>
                    </div>

                    @if($isEnrolled && $userProgress)
                        <div class="mb-6 p-4 bg-green-900/20 border border-green-700 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-green-400 font-medium">Your Progress</span>
                                <span class="text-green-400 text-sm">{{ $progressPercentage }}% Complete</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: {{ $progressPercentage }}%"></div>
                            </div>
                        </div>
                    @endif

                    <div class="space-y-4">
                        @foreach($course->chapters as $chapterIndex => $chapter)
                            <div class="border border-gray-600 rounded-lg overflow-hidden">
                                <div class="bg-gray-700 px-6 py-4 cursor-pointer" onclick="toggleChapter({{ $chapterIndex }})">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <svg class="w-5 h-5 text-gray-400 transform transition-transform chapter-arrow rotate" id="arrow-{{ $chapterIndex }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                            <h3 class="text-lg font-semibold text-white flex items-center space-x-2">
                                                <span>{{ $chapter->title }}</span>
                                                @if(!$chapter->is_published)
                                                    <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs font-medium">Draft</span>
                                                @endif
                                            </h3>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            {{ $chapter->lectures->count() }} lectures •
                                            {{ floor($chapter->lectures->sum('duration_minutes') / 60) }}h {{ $chapter->lectures->sum('duration_minutes') % 60 }}m
                                        </div>
                                    </div>
                                    @if($chapter->description)
                                        <p class="text-gray-400 text-sm mt-2">{{ $chapter->description }}</p>
                                    @endif
                                </div>

                                <div class="chapter-content show" id="chapter-{{ $chapterIndex }}">
                                    @if($chapter->lectures->count() > 0)
                                        @foreach($chapter->lectures as $lecture)
                                            @if($isEnrolled)
                                                <a href="{{ route('my-courses.lecture', [$course, $lecture]) }}"
                                                   class="block px-6 py-3 border-t border-gray-600 hover:bg-gray-750 transition-colors">
                                            @elseif($lecture->is_free_preview)
                                                <button class="preview-btn w-full text-left px-6 py-3 border-t border-gray-600 hover:bg-gray-750 hover:border-blue-500 transition-colors cursor-pointer"
                                                        data-lecture-id="{{ $lecture->id }}"
                                                        data-lecture-title="{{ htmlspecialchars($lecture->title, ENT_QUOTES, 'UTF-8') }}"
                                                        data-lecture-type="{{ $lecture->type }}"
                                                        data-lecture-content="{{ htmlspecialchars($lecture->content ?? '', ENT_QUOTES, 'UTF-8') }}"
                                                        data-lecture-description="{{ htmlspecialchars($lecture->description ?? '', ENT_QUOTES, 'UTF-8') }}"
                                                        title="Click to preview this lecture">
                                            @else
                                                <div class="px-6 py-3 border-t border-gray-600 opacity-75">
                                            @endif
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-3">
                                                        <!-- Lecture Type Icon -->
                                                        @switch($lecture->type)
                                                            @case('video')
                                                                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-6V7a2 2 0 00-2-2H5a2 2 0 00-2 2v3m2 4h10a2 2 0 002-2v-3a2 2 0 00-2-2H5a2 2 0 00-2 2v3z"></path>
                                                                </svg>
                                                                @break
                                                            @case('text')
                                                                <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                                </svg>
                                                                @break
                                                            @case('quiz')
                                                                <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                @break
                                                            @case('assignment')
                                                                <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                                                </svg>
                                                                @break
                                                            @case('resource')
                                                                <svg class="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                                </svg>
                                                                @break
                                                            @default
                                                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                                </svg>
                                                        @endswitch

                                                        <!-- Completion Status -->
                                                        @if($isEnrolled && in_array($lecture->id, $completedLectureIds))
                                                            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                        @elseif($isEnrolled)
                                                            <div class="w-5 h-5 border-2 border-gray-500 rounded-full"></div>
                                                        @elseif($lecture->is_free_preview)
                                                            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                            </svg>
                                                        @else
                                                            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                                            </svg>
                                                        @endif

                                                        <div>
                                                            <h4 class="text-white font-medium flex items-center space-x-2">
                                                                <span>{{ $lecture->title }}</span>
                                                                @if(!$lecture->is_published)
                                                                    <span class="bg-red-600 text-white px-2 py-1 rounded text-xs font-medium">Draft</span>
                                                                @endif
                                                            </h4>
                                                            @if($lecture->description)
                                                                <p class="text-gray-400 text-sm">{{ Str::limit($lecture->description, 80) }}</p>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <div class="flex items-center space-x-4">
                                                        @if($lecture->is_free_preview)
                                                            <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium flex items-center space-x-1">
                                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-6V7a2 2 0 00-2-2H5a2 2 0 00-2 2v3m2 4h10a2 2 0 002-2v-3a2 2 0 00-2-2H5a2 2 0 00-2 2v3z"></path>
                                                                </svg>
                                                                <span>Preview</span>
                                                            </span>
                                                        @endif

                                                        @if($lecture->duration_minutes > 0)
                                                            <span class="text-gray-400 text-sm">{{ $lecture->duration_minutes }}m</span>
                                                        @endif

                                                        @if($isEnrolled)
                                                            <span class="text-blue-400 text-sm font-medium">
                                                                {{ in_array($lecture->id, $completedLectureIds) ? 'Review' : 'Start' }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            @if($isEnrolled)
                                                </a>
                                            @elseif($lecture->is_free_preview)
                                                </button>
                                            @else
                                                </div>
                                            @endif
                                        @endforeach
                                    @else
                                        <div class="px-6 py-4 text-gray-400 text-center">
                                            No lectures available in this chapter yet.
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Course Requirements -->
                @if($course->requirements && is_array($course->requirements) && count($course->requirements) > 0)
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-8">
                    <h2 class="text-2xl font-bold text-white mb-6">Requirements</h2>
                    <ul class="space-y-2">
                        @foreach($course->requirements as $requirement)
                            <li class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span class="text-gray-300">{{ $requirement }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <!-- Target Audience -->
                @if($course->target_audience && is_array($course->target_audience) && count($course->target_audience) > 0)
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-8">
                    <h2 class="text-2xl font-bold text-white mb-6">Who This Course Is For</h2>
                    <ul class="space-y-2">
                        @foreach($course->target_audience as $audience)
                            <li class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span class="text-gray-300">{{ $audience }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Instructor Info -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Your Instructor</h3>
                    <div class="flex items-center space-x-4">
                        <img src="{{ $course->instructor->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($course->instructor->name) . '&background=4f46e5&color=fff' }}" 
                             alt="{{ $course->instructor->name }}" 
                             class="w-12 h-12 rounded-full">
                        <div>
                            <h4 class="text-white font-medium">{{ $course->instructor->name }}</h4>
                            @if($course->instructor->bio)
                                <p class="text-gray-400 text-sm mt-1">{{ Str::limit($course->instructor->bio, 100) }}</p>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Course Stats -->
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Course Details</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Level</span>
                            <span class="text-white">{{ ucfirst($course->level) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Duration</span>
                            <span class="text-white">{{ floor($totalDuration / 60) }}h {{ $totalDuration % 60 }}m</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Lectures</span>
                            <span class="text-white">{{ $totalLectures }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Chapters</span>
                            <span class="text-white">{{ $course->chapters->count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Category</span>
                            <span class="text-white">{{ $course->category ? $course->category->name : $course->category }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Language</span>
                            <span class="text-white">{{ $course->language }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Students</span>
                            <span class="text-white">{{ $course->total_students ?? 0 }}</span>
                        </div>
                        @if($course->average_rating > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-400">Rating</span>
                            <div class="flex items-center space-x-1">
                                <span class="text-yellow-400">{{ number_format($course->average_rating, 1) }}</span>
                                <div class="flex">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= floor($course->average_rating))
                                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @else
                                            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @endif
                                    @endfor
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Courses -->
@if($relatedCourses->count() > 0)
<section class="py-16 bg-black">
    <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-white mb-12 text-center">Related Courses</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($relatedCourses as $relatedCourse)
                <div class="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden hover:border-red-500 transition-all duration-300">
                    @if($relatedCourse->image)
                        <img src="{{ asset('storage/' . $relatedCourse->image) }}" 
                             alt="{{ $relatedCourse->title }}" 
                             class="w-full h-48 object-cover">
                    @else
                        <div class="w-full h-48 bg-gray-700 flex items-center justify-center">
                            <svg class="w-16 h-16 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    @endif
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-white mb-2">{{ $relatedCourse->title }}</h3>
                        <p class="text-gray-400 text-sm mb-4 line-clamp-2">{{ Str::limit($relatedCourse->description, 100) }}</p>
                        <div class="flex items-center justify-between">
                            <span class="text-red-500 font-bold">${{ number_format($relatedCourse->price, 2) }}</span>
                            <a href="{{ route('courses.show', $relatedCourse) }}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm transition-colors">
                                View Course
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Preview Modal -->
<div id="previewModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
            <div>
                <h3 id="modalTitle" class="text-xl font-semibold text-white">Lecture Preview</h3>
                <p class="text-gray-400 text-sm mt-1">Free preview - Sign up to access the full course</p>
            </div>
            <button onclick="closePreviewModal()" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Modal Content -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            <div id="modalContent" class="space-y-4">
                <!-- Content will be loaded here -->
            </div>
            
            <!-- Call to Action -->
            <div class="mt-8 p-6 bg-gradient-to-r from-red-600 to-blue-600 rounded-lg text-center">
                <h4 class="text-xl font-semibold text-white mb-2">Want to see more?</h4>
                <p class="text-gray-200 mb-4">Get full access to this course and {{ $totalLectures - 1 }} more lectures</p>
                @auth
                    @if(!$isEnrolled)
                        <form action="{{ route('paypal.create', $course) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-white text-gray-900 px-6 py-3 rounded-md font-semibold hover:bg-gray-100 transition-colors">
                                Enroll Now - ${{ number_format($course->price, 2) }}
                            </button>
                        </form>
                    @endif
                @else
                    <div class="space-x-3">
                        <a href="{{ route('login') }}" class="bg-white text-gray-900 px-6 py-3 rounded-md font-semibold hover:bg-gray-100 transition-colors inline-block">
                            Sign In to Enroll
                        </a>
                        <a href="{{ route('register') }}" class="border border-white text-white px-6 py-3 rounded-md font-semibold hover:bg-white hover:text-gray-900 transition-colors inline-block">
                            Create Account
                        </a>
                    </div>
                @endauth
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.chapter-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.chapter-content.show {
    max-height: 1000px;
    transition: max-height 0.3s ease-in;
}

.chapter-arrow {
    transition: transform 0.3s ease;
}

.chapter-arrow.rotate {
    transform: rotate(90deg);
}
</style>
@endpush

@push('scripts')
<script>
// Chapter toggle functionality
function toggleChapter(chapterIndex) {
    const content = document.getElementById(`chapter-${chapterIndex}`);
    const arrow = document.getElementById(`arrow-${chapterIndex}`);

    if (content.classList.contains('show')) {
        content.classList.remove('show');
        arrow.classList.remove('rotate');
    } else {
        content.classList.add('show');
        arrow.classList.add('rotate');
    }
}

// Modal functionality
const PreviewModal = {
    modal: null,
    modalTitle: null,
    modalContent: null,
    
    init() {
        this.modal = document.getElementById('previewModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalContent = document.getElementById('modalContent');
        
        // Add event listeners
        this.addEventListeners();
    },
    
    addEventListeners() {
        // Preview button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.preview-btn')) {
                e.preventDefault();
                this.openModal(e.target.closest('.preview-btn'));
            }
        });
        
        // Close button
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick="closePreviewModal()"]')) {
                e.preventDefault();
                this.closeModal();
            }
        });
        
        // Click outside modal
        document.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
                this.closeModal();
            }
        });
    },
    
    openModal(button) {
        try {
            if (!this.modal || !this.modalTitle || !this.modalContent) {
                console.error('Modal elements not found');
                return;
            }
            
            // Get data from button attributes
            const lectureId = button.dataset.lectureId;
            const title = button.dataset.lectureTitle || 'Lecture Preview';
            const type = button.dataset.lectureType || 'text';
            const content = button.dataset.lectureContent || '';
            const description = button.dataset.lectureDescription || '';
            
            // Set modal title
            this.modalTitle.textContent = title;
            
            // Generate content HTML
            let contentHtml = '';
            
            // Add description if available
            if (description.trim()) {
                contentHtml += `<div class="mb-4"><p class="text-gray-300">${this.escapeHtml(description)}</p></div>`;
            }
            
            // Render content based on type
            contentHtml += this.renderContentByType(type, content);
            
            // Set modal content
            this.modalContent.innerHTML = contentHtml;
            
            // Show modal
            this.modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            
        } catch (error) {
            console.error('Error opening preview modal:', error);
            alert('Sorry, there was an error opening the preview. Please try again.');
        }
    },
    
    renderContentByType(type, content) {
        switch(type) {
            case 'video':
                return this.renderVideo(content);
            case 'text':
                return this.renderText(content);
            case 'quiz':
                return this.renderQuiz();
            case 'assignment':
                return this.renderAssignment();
            case 'resource':
                return this.renderResource();
            default:
                return this.renderDefault();
        }
    },
    
    renderVideo(content) {
        if (content.includes('youtube.com') || content.includes('youtu.be')) {
            let videoId = '';
            if (content.includes('youtube.com/watch?v=')) {
                videoId = content.split('v=')[1].split('&')[0];
            } else if (content.includes('youtu.be/')) {
                videoId = content.split('youtu.be/')[1].split('?')[0];
            }
            return `
                <div class="aspect-video bg-black rounded-lg overflow-hidden">
                    <iframe src="https://www.youtube.com/embed/${videoId}" 
                            class="w-full h-full" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                    </iframe>
                </div>`;
        } else {
            return `
                <div class="aspect-video bg-black rounded-lg overflow-hidden">
                    <video controls class="w-full h-full">
                        <source src="${this.escapeHtml(content)}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>`;
        }
    },
    
    renderText(content) {
        return `
            <div class="bg-gray-900 rounded-lg p-6">
                <div class="prose prose-invert max-w-none">
                    ${this.escapeHtml(content).replace(/\n/g, '<br>')}
                </div>
            </div>`;
    },
    
    renderQuiz() {
        return `
            <div class="bg-yellow-900/20 border border-yellow-700 rounded-lg p-6 text-center">
                <svg class="w-16 h-16 text-yellow-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h4 class="text-xl font-semibold text-white mb-2">Interactive Quiz</h4>
                <p class="text-gray-300">This lecture contains an interactive quiz. Enroll to access the full quiz experience.</p>
            </div>`;
    },
    
    renderAssignment() {
        return `
            <div class="bg-purple-900/20 border border-purple-700 rounded-lg p-6 text-center">
                <svg class="w-16 h-16 text-purple-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h4 class="text-xl font-semibold text-white mb-2">Assignment</h4>
                <p class="text-gray-300">This lecture contains a hands-on assignment. Enroll to access the full assignment details and submit your work.</p>
            </div>`;
    },
    
    renderResource() {
        return `
            <div class="bg-orange-900/20 border border-orange-700 rounded-lg p-6 text-center">
                <svg class="w-16 h-16 text-orange-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h4 class="text-xl font-semibold text-white mb-2">Downloadable Resource</h4>
                <p class="text-gray-300">This lecture contains downloadable resources and materials. Enroll to access and download all course materials.</p>
            </div>`;
    },
    
    renderDefault() {
        return `
            <div class="bg-gray-900 rounded-lg p-6 text-center">
                <h4 class="text-xl font-semibold text-white mb-2">Preview Available</h4>
                <p class="text-gray-300">This is a preview of the lecture content. Enroll to access the full lecture.</p>
            </div>`;
    },
    
    closeModal() {
        if (!this.modal) return;
        
        this.modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
        
        // Stop any playing videos
        const videos = this.modal.querySelectorAll('video');
        videos.forEach(video => {
            video.pause();
            video.currentTime = 0;
        });
        
        // Stop YouTube videos by reloading iframe
        const iframes = this.modal.querySelectorAll('iframe');
        iframes.forEach(iframe => {
            const src = iframe.src;
            iframe.src = '';
            iframe.src = src;
        });
    },
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
};

// Legacy function for backward compatibility
function closePreviewModal() {
    PreviewModal.closeModal();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    PreviewModal.init();
});
</script>
@endpush
