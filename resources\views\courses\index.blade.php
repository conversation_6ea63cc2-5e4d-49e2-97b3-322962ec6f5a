@extends('layouts.app')

@section('title', 'Courses - Escape Matrix Academy')

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-black via-gray-900 to-black">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Transform Your <span class="text-red-500">Future</span>
            </h1>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Choose from our comprehensive collection of courses designed to help you break free from limitations and achieve extraordinary results.
            </p>
        </div>
    </div>
</section>

<!-- Filters and Search -->
<section class="py-8 bg-gray-900 border-b border-gray-800">
    <div class="container mx-auto px-4">
        <form method="GET" action="{{ route('courses.index') }}" class="space-y-6">
            <!-- Search Bar -->
            <div class="relative max-w-2xl mx-auto">
                <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <input type="text" name="search" placeholder="Search for courses..." value="{{ request('search') }}"
                       class="pl-12 pr-4 w-full py-3 bg-gray-800 border border-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-lg">
            </div>

            <!-- Filter Options -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Category Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                    <select name="category" class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                        <option value="all" {{ request('category') === 'all' || !request('category') ? 'selected' : '' }}>All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->slug }}" {{ request('category') === $category->slug ? 'selected' : '' }}>{{ $category->name }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Level Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Level</label>
                    <select name="level" class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                        <option value="all" {{ request('level') === 'all' || !request('level') ? 'selected' : '' }}>All Levels</option>
                        @foreach($levels as $level)
                            <option value="{{ $level }}" {{ request('level') === $level ? 'selected' : '' }}>{{ ucfirst(str_replace('_', ' ', $level)) }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Price Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Price</label>
                    <select name="price_range" class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                        <option value="all" {{ request('price_range') === 'all' || !request('price_range') ? 'selected' : '' }}>All Prices</option>
                        @foreach($priceRanges as $key => $label)
                            <option value="{{ $key }}" {{ request('price_range') === $key ? 'selected' : '' }}>{{ $label }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Sort Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Sort By</label>
                    <select name="sort" class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white rounded-md focus:outline-none focus:ring-red-500 focus:border-red-500">
                        <option value="newest" {{ request('sort') === 'newest' || !request('sort') ? 'selected' : '' }}>Newest</option>
                        <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                        <option value="rating" {{ request('sort') === 'rating' ? 'selected' : '' }}>Highest Rated</option>
                        <option value="price_low" {{ request('sort') === 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') === 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                    </select>
                </div>
            </div>

            <!-- Filter Actions -->
            <div class="flex justify-center gap-4">
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-md transition-colors font-medium">
                    Apply Filters
                </button>
                <a href="{{ route('courses.index') }}" class="border border-gray-600 text-gray-300 hover:bg-gray-800 px-6 py-2 rounded-md transition-colors font-medium">
                    Clear All
                </a>
            </div>
        </form>
    </div>
</section>

<!-- Results Summary -->
<section class="py-6 bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center">
            <p class="text-gray-300">
                Showing {{ $courses->firstItem() ?? 0 }} - {{ $courses->lastItem() ?? 0 }} of {{ $courses->total() }} courses
            </p>
            @if(request()->hasAny(['search', 'category', 'level', 'price_range']))
                <div class="flex items-center gap-2 text-sm">
                    <span class="text-gray-400">Active filters:</span>
                    @if(request('search'))
                        <span class="bg-red-600/20 text-red-400 px-2 py-1 rounded text-xs">Search: "{{ request('search') }}"</span>
                    @endif
                    @if(request('category') && request('category') !== 'all')
                        @php
                            $selectedCategory = $categories->firstWhere('slug', request('category'));
                        @endphp
                        <span class="bg-red-600/20 text-red-400 px-2 py-1 rounded text-xs">{{ $selectedCategory ? $selectedCategory->name : ucfirst(request('category')) }}</span>
                    @endif
                    @if(request('level') && request('level') !== 'all')
                        <span class="bg-red-600/20 text-red-400 px-2 py-1 rounded text-xs">{{ ucfirst(str_replace('_', ' ', request('level'))) }}</span>
                    @endif
                </div>
            @endif
        </div>
    </div>
</section>

<!-- Courses Grid -->
<section class="py-16 bg-black">
    <div class="container mx-auto px-4">
        @if($courses->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($courses as $course)
                    <div class="bg-gray-800 border border-gray-700 rounded-xl overflow-hidden hover:border-red-500 hover:shadow-2xl hover:shadow-red-500/10 transition-all duration-300 group">
                        <div class="relative">
                            <img src="{{ $course->image ? asset('storage/' . $course->image) : asset('images/course-template.svg') }}"
                                 alt="{{ $course->title }}"
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">

                            <!-- Course Level Badge -->
                            <div class="absolute top-3 left-3">
                                <span class="bg-red-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                                    {{ ucfirst(str_replace('_', ' ', $course->level)) }}
                                </span>
                            </div>

                            <!-- Price Badge -->
                            <div class="absolute top-3 right-3">
                                @if($course->price == 0)
                                    <span class="bg-green-600 text-white px-2 py-1 rounded-full text-xs font-bold">FREE</span>
                                @else
                                    <div class="bg-black/80 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs">
                                        @if($course->original_price && $course->original_price > $course->price)
                                            <span class="line-through text-gray-400">${{ number_format($course->original_price, 0) }}</span>
                                            <span class="font-bold text-red-400 ml-1">${{ number_format($course->price, 0) }}</span>
                                        @else
                                            <span class="font-bold">${{ number_format($course->price, 0) }}</span>
                                        @endif
                                    </div>
                                @endif
                            </div>

                            <!-- Featured Badge -->
                            @if($course->featured)
                                <div class="absolute bottom-3 left-3">
                                    <span class="bg-yellow-500 text-black px-2 py-1 rounded-full text-xs font-bold">
                                        ⭐ FEATURED
                                    </span>
                                </div>
                            @endif
                        </div>

                        <div class="p-5">
                            <!-- Course Category -->
                            <div class="flex items-center gap-2 mb-2">
                                <span class="text-red-400 text-xs font-medium">{{ $course->category ? $course->category->name : $course->category }}</span>
                                @if($course->subcategory)
                                    <span class="text-gray-500 text-xs">•</span>
                                    <span class="text-gray-400 text-xs">{{ $course->subcategory->name ?? $course->subcategory }}</span>
                                @endif
                            </div>

                            <!-- Course Title -->
                            <h3 class="text-lg font-bold text-white mb-2 group-hover:text-red-400 transition-colors line-clamp-2 min-h-[3.5rem]">
                                {{ $course->title }}
                            </h3>

                            <!-- Course Subtitle -->
                            @if($course->subtitle)
                                <p class="text-gray-400 text-sm mb-3 line-clamp-2">{{ $course->subtitle }}</p>
                            @endif

                            <!-- Instructor -->
                            <div class="flex items-center gap-2 mb-3">
                                <div class="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">{{ substr($course->instructor->name ?? 'I', 0, 1) }}</span>
                                </div>
                                <span class="text-gray-400 text-sm">{{ $course->instructor->name ?? 'Instructor' }}</span>
                            </div>

                            <!-- Course Stats -->
                            <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                                <div class="flex items-center gap-3">
                                    <!-- Rating -->
                                    @if($course->average_rating > 0)
                                        <div class="flex items-center gap-1">
                                            <div class="flex">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= floor($course->average_rating))
                                                        <svg class="w-3 h-3 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    @else
                                                        <svg class="w-3 h-3 text-gray-600 fill-current" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    @endif
                                                @endfor
                                            </div>
                                            <span>{{ number_format($course->average_rating, 1) }}</span>
                                        </div>
                                    @endif

                                    <!-- Students Count -->
                                    @if($course->total_students > 0)
                                        <div class="flex items-center gap-1">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                            </svg>
                                            <span>{{ number_format($course->total_students) }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Duration -->
                                @if($course->total_duration_minutes > 0)
                                    <div class="flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>{{ floor($course->total_duration_minutes / 60) }}h {{ $course->total_duration_minutes % 60 }}m</span>
                                    </div>
                                @endif
                            </div>

                            <!-- Action Button -->
                            @if($course->is_enrolled)
                                <a href="{{ route('my-courses.view', $course) }}" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors inline-block text-center font-medium">
                                    Continue Learning
                                </a>
                            @else
                                <a href="{{ route('courses.show', $course) }}" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition-colors inline-block text-center font-medium">
                                    View Course
                                </a>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-16">
                <div class="flex justify-center">
                    {{ $courses->links('pagination::tailwind') }}
                </div>
            </div>
        @else
            <div class="text-center py-20">
                <div class="max-w-md mx-auto">
                    <svg class="w-24 h-24 text-gray-600 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.816-6.207-2.175.193-.39.398-.844.617-1.364.24-.57.51-1.207.817-1.897C8.448 7.302 10.132 6 12 6c1.868 0 3.552 1.302 4.773 3.564.307.69.577 1.327.817 1.897.219.52.424.974.617 1.364A7.962 7.962 0 0112 15z"></path>
                    </svg>
                    <h3 class="text-2xl font-bold text-white mb-4">No courses found</h3>
                    <p class="text-gray-400 mb-8">We couldn't find any courses matching your criteria. Try adjusting your search or filter options.</p>
                    <div class="space-y-3">
                        <a href="{{ route('courses.index') }}" class="block bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors font-medium">
                            View All Courses
                        </a>
                        @if(request()->hasAny(['search', 'category', 'level', 'price_range']))
                            <button onclick="clearFilters()" class="block w-full border border-gray-600 text-gray-300 hover:bg-gray-800 px-6 py-3 rounded-lg transition-colors font-medium">
                                Clear All Filters
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>
@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Custom pagination styles */
.pagination {
    @apply flex items-center space-x-1;
}

.pagination .page-link {
    @apply px-3 py-2 text-sm text-gray-300 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white transition-colors;
}

.pagination .page-item.active .page-link {
    @apply bg-red-600 text-white border-red-600;
}

.pagination .page-item.disabled .page-link {
    @apply text-gray-500 cursor-not-allowed hover:bg-gray-800 hover:text-gray-500;
}
</style>
@endpush

@push('scripts')
<script>
function clearFilters() {
    window.location.href = '{{ route("courses.index") }}';
}

// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select');

    selects.forEach(select => {
        select.addEventListener('change', function() {
            // Small delay to allow user to see the change
            setTimeout(() => {
                form.submit();
            }, 100);
        });
    });
});
</script>
@endpush
